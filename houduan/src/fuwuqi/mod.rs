#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

pub mod jiamichuanshu;
mod luyou;

use rocket::{get, routes, Rocket, Build};
use rocket::serde::json::Json;
use serde::{Deserialize, Serialize};

use jiamichuanshu::miyaochuanshu::{miyaojiaohuan_guanliqii, get_miyao_jiaohuan_routes};

/// 服务器响应结构体
#[derive(Serialize, Deserialize)]
pub struct fuwuqi_xiangying {
    pub chenggong: bool,
    pub xiaoxi: String,
    pub shuju: Option<serde_json::Value>,
}

/// 健康检查接口
#[get("/health")]
pub fn jiankang_jiancha() -> Json<fuwuqi_xiangying> {
    Json(fuwuqi_xiangying {
        chenggong: true,
        xiaoxi: "服务器运行正常".to_string(),
        shuju: None,
    })
}

/// 获取系统信息接口
#[get("/info")]
pub fn xitong_xinxi() -> <PERSON><PERSON><fuwuqi_xiangying> {
    Json(fuwuqi_xiangying {
        chenggong: true,
        xiaoxi: "RO百科资料站系统".to_string(),
        shuju: Some(serde_json::json!({
            "banben": "1.0.0",
            "miaoshu": "RO百科资料站后端服务"
        })),
    })
}

/// 构建 Rocket 应用（不带Redis）
pub fn goujian_fuwuqi() -> Rocket<Build> {
    // 创建密钥交换管理器（仅内存版本）
    let miyao_guanliqii = miyaojiaohuan_guanliqii::new();

    rocket::build()
        .mount("/api", routes![jiankang_jiancha, xitong_xinxi])
        .mount("/api/crypto", get_miyao_jiaohuan_routes())
        .manage(miyao_guanliqii)
}

/// 构建 Rocket 应用（带Redis支持）
pub fn goujian_fuwuqi_with_redis(redis_guanliqi: std::sync::Arc<crate::chushihua::shujukuxitong::redishujuku::redis_lianjie_guanli>) -> Rocket<Build> {
    // 创建带Redis支持的密钥交换管理器
    let miyao_guanliqii = miyaojiaohuan_guanliqii::new_with_redis(redis_guanliqi);

    rocket::build()
        .mount("/api", routes![jiankang_jiancha, xitong_xinxi])
        .mount("/api/crypto", get_miyao_jiaohuan_routes())
        .manage(miyao_guanliqii)
}
