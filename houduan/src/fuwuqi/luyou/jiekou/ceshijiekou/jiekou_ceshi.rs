#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use rocket::{get, routes, Route};
use rocket::serde::json::Json;
use crate::fuwuqi::luyou::luyoujiegouti_chuli::mingwen_xiangying;
use crate::fuwuqi::luyou::{luyou_qingqiu_kaishi, luyou_qingqiu_wancheng, luyou_rizhi_xinxi};

/// 测试接口处理函数
#[get("/ceshijiekou")]
pub fn jiekou_ceshi_chuli() -> Json<mingwen_xiangying> {
    let jiekou_ming = "测试接口";
    let qingqiu_lujing = "/ceshijiekou";
    let qingqiu_fangfa = "GET";

    // 记录请求开始
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);

    let kaishi_shijian = std::time::Instant::now();

    // 创建响应
    let xiangying = mingwen_xiangying::jiandan_cheng<PERSON>(
        "欢迎使用小落RO仙境传说资料站".to_string()
    );

    // 计算处理时间
    let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;

    // 记录请求完成
    luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);

    // 记录接口调用信息
    luyou_rizhi_xinxi(jiekou_ming, "测试接口调用成功");

    Json(xiangying)
}

/// 测试接口结构体
pub struct jiekou_ceshi;

impl jiekou_ceshi {
    /// 获取接口路径
    pub fn get_lujing() -> &'static str {
        "/ceshijiekou"
    }

    /// 获取HTTP方法
    pub fn get_fangfa() -> &'static str {
        "GET"
    }

    /// 获取接口描述
    pub fn get_miaoshu() -> &'static str {
        "测试接口 - 返回欢迎信息"
    }

    /// 获取接口介绍
    pub fn get_jieshao() -> &'static str {
        "这是一个测试接口，用于验证路由系统是否正常工作。返回欢迎使用小落RO仙境传说资料站的消息。"
    }

    /// 获取Rocket路由
    pub fn get_routes() -> Vec<Route> {
        routes![jiekou_ceshi_chuli]
    }
}
