#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

pub mod jiekou_ceshi;

use rocket::Route;

/// 获取测试接口模块的所有路由
pub fn get_routes() -> Vec<Route> {
    jiekou_ceshi::jiekou_ceshi::get_routes()
}

/// 获取测试接口模块的所有接口信息
pub fn get_jiekou_xinxi() -> Vec<(String, String, String, String)> {
    vec![
        (
            jiekou_ceshi::jiekou_ceshi::get_lujing().to_string(),
            jiekou_ceshi::jiekou_ceshi::get_fangfa().to_string(),
            jiekou_ceshi::jiekou_ceshi::get_miaoshu().to_string(),
            jiekou_ceshi::jiekou_ceshi::get_jieshao().to_string(),
        )
    ]
}
