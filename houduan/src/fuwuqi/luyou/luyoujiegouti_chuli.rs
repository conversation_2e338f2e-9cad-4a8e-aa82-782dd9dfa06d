#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use serde::{Deserialize, Serialize};

/// 明文传输接口通用响应结构体
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct mingwen_xiangying {
    /// 请求是否成功
    pub chenggong: bool,
    /// 响应消息
    pub xiaoxi: String,
    /// 响应数据（可选）
    pub shuju: Option<serde_json::Value>,
    /// 时间戳
    pub shijian: i64,
}

impl mingwen_xiangying {
    /// 创建成功响应
    pub fn chenggong_xiangying(xiaoxi: String, shuju: Option<serde_json::Value>) -> Self {
        Self {
            chenggong: true,
            xiaoxi,
            shuju,
            shijian: chrono::Utc::now().timestamp(),
        }
    }

    /// 创建失败响应
    pub fn shibai_xiangying(xiaoxi: String) -> Self {
        Self {
            chenggong: false,
            xiaoxi,
            shuju: None,
            shijian: chrono::Utc::now().timestamp(),
        }
    }

    /// 创建简单成功响应（无数据）
    pub fn jiandan_chenggong(xiaoxi: String) -> Self {
        Self::chenggong_xiangying(xiaoxi, None)
    }

    /// 创建带数据的成功响应
    pub fn chenggong_with_shuju(xiaoxi: String, shuju: serde_json::Value) -> Self {
        Self::chenggong_xiangying(xiaoxi, Some(shuju))
    }
}
