#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use rocket::Route;
use crate::fuwuqi::luyou::jiekou::ceshijiekou::jiekou_ceshi;
use crate::fuwuqi::luyou::{luyou_zhuce_rizhi, luyou_chushihua_rizhi};

/// 路由接口特征
pub trait luyou_jiekou_trait {
    fn get_lujing() -> &'static str;
    fn get_fangfa() -> &'static str;
    fn get_miaoshu() -> &'static str;
    fn get_routes() -> Vec<Route>;
}

/// 为测试接口实现路由特征
impl luyou_jiekou_trait for jiekou_ceshi {
    fn get_lujing() -> &'static str {
        Self::get_lujing()
    }

    fn get_fangfa() -> &'static str {
        Self::get_fangfa()
    }

    fn get_miaoshu() -> &'static str {
        Self::get_miaoshu()
    }

    fn get_routes() -> Vec<Route> {
        Self::get_routes()
    }
}

/// 路由管理器
pub struct luyou_guanliqii {
    jiekou_xinxi: Vec<(String, String, String)>, // (路径, 方法, 描述)
}

impl luyou_guanliqii {
    /// 创建新的路由管理器
    pub fn new() -> Self {
        let mut guanliqii = Self {
            jiekou_xinxi: Vec::new(),
        };

        // 自动注册所有接口
        guanliqii.zhuce_suoyou_jiekou();
        
        guanliqii
    }

    /// 注册单个接口信息
    pub fn zhuce_jiekou<T: luyou_jiekou_trait>(&mut self) {
        let lujing = T::get_lujing().to_string();
        let fangfa = T::get_fangfa().to_string();
        let miaoshu = T::get_miaoshu().to_string();

        self.jiekou_xinxi.push((lujing.clone(), fangfa.clone(), miaoshu.clone()));
        
        // 记录路由注册日志
        luyou_zhuce_rizhi(&miaoshu, &lujing, &fangfa);
    }

    /// 注册所有接口
    fn zhuce_suoyou_jiekou(&mut self) {
        // 注册测试接口
        self.zhuce_jiekou::<jiekou_ceshi>();
        
        // 记录初始化完成日志
        luyou_chushihua_rizhi(self.jiekou_xinxi.len());
    }

    /// 获取所有路由用于Rocket挂载
    pub fn get_suoyou_luyou() -> Vec<Route> {
        let mut luyou = Vec::new();

        // 收集所有接口的路由
        luyou.extend(jiekou_ceshi::get_routes());

        luyou
    }

    /// 获取接口列表信息
    pub fn get_jiekou_liebiao(&self) -> &Vec<(String, String, String)> {
        &self.jiekou_xinxi
    }

    /// 获取接口数量
    pub fn get_jiekou_shuliang(&self) -> usize {
        self.jiekou_xinxi.len()
    }

    /// 根据路径查找接口信息
    pub fn chazhao_jiekou_by_lujing(&self, lujing: &str) -> Option<&(String, String, String)> {
        self.jiekou_xinxi.iter().find(|(l, _, _)| l == lujing)
    }

    /// 根据方法查找接口列表
    pub fn chazhao_jiekou_by_fangfa(&self, fangfa: &str) -> Vec<&(String, String, String)> {
        self.jiekou_xinxi.iter().filter(|(_, f, _)| f == fangfa).collect()
    }
}
